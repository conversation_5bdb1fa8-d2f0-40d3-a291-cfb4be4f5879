import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';
import '../services/api_service.dart';
import '../services/backend_api_service.dart';
import '../services/payment_processor.dart';
import 'no_api_key_screen.dart';
import 'no_subscription_screen.dart';

class RecentConversationsScreen extends StatefulWidget {
  final ApiService apiService;

  const RecentConversationsScreen({
    super.key,
    required this.apiService,
  });

  @override
  State<RecentConversationsScreen> createState() => _RecentConversationsScreenState();
}

class _RecentConversationsScreenState extends State<RecentConversationsScreen> {
  bool _isLoading = true;
  String? _errorMessage;
  Map<String, dynamic>? _summaryData;
  List<Map<String, dynamic>> _conversations = [];
  bool _hasApiKey = false;
  bool _hasActiveSubscription = false;
  int? _userId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getUserIdFromRoute();
    });
  }

  void _getUserIdFromRoute() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null && args.containsKey('userId')) {
      _userId = args['userId'] as int;
      print('RecentConversationsScreen: Got userId from route: $_userId');
    }
    _checkApiKeyAndLoadData();
  }

  // Get responsive padding based on screen size
  EdgeInsets _getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width >= 768) {
      return const EdgeInsets.all(24);
    }
    return const EdgeInsets.all(16);
  }

  // Get responsive card width
  double? _getCardMaxWidth(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width >= 768) {
      return 600;
    }
    return null;
  }

  // Get current time slot (AM or PM)
  String _getCurrentTimeSlot() {
    final now = DateTime.now();
    return now.hour < 12 ? 'AM' : 'PM';
  }

  Future<void> _checkApiKeyAndLoadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final apiKey = prefs.getString('user_api_key');

      if (apiKey == null || apiKey.isEmpty) {
        setState(() {
          _hasApiKey = false;
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _hasApiKey = true;
      });

      // Get current user ID
      final userId = await BackendApiService.getCurrentUserId();
      if (userId == null) {
        setState(() {
          _errorMessage = 'Unable to get user information';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _userId = userId;
      });

      // Check subscription status
      bool hasActiveSubscription = await PaymentProcessor.verifyActiveSubscription(userId);

      // If no active subscription found and user ID is valid, try syncing with Stripe
      if (!hasActiveSubscription && userId > 0) {
        debugPrint('No active subscription found for user $userId, attempting to sync with Stripe...');
        try {
          await PaymentProcessor.syncWithStripe(userId);
          // Re-check subscription status after sync
          hasActiveSubscription = await PaymentProcessor.verifyActiveSubscription(userId);
          debugPrint('After sync, subscription status: $hasActiveSubscription');
        } catch (syncError) {
          debugPrint('Error syncing with Stripe: $syncError');
          // Continue with original subscription status
        }
      } else if (userId <= 0) {
        debugPrint('Invalid user ID: $userId, skipping subscription check');
      }

      setState(() {
        _hasActiveSubscription = hasActiveSubscription;
      });

      if (!hasActiveSubscription) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      await _loadSummaryAndConversations();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading conversation summary: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSummaryAndConversations() async {
    if (!_hasApiKey || !_hasActiveSubscription) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final currentSlot = _getCurrentTimeSlot();
      final prefs = await SharedPreferences.getInstance();
      final cachedSummaryString = prefs.getString('summary_cached_data');
      final lastSlot = prefs.getString('summary_last_slot');

      if (currentSlot == lastSlot && cachedSummaryString != null) {
        final cachedSummary = json.decode(cachedSummaryString) as Map<String, dynamic>;
        setState(() {
          _summaryData = cachedSummary;
          _extractConversationsFromSummary(cachedSummary);
        });
      } else {
        final summary = await widget.apiService.getSummary();

        if (cachedSummaryString != null) {
          final cachedSummary = json.decode(cachedSummaryString) as Map<String, dynamic>;
          final isEqual = const DeepCollectionEquality().equals(summary, cachedSummary);

          if (!isEqual) {
            await prefs.setString('summary_cached_data', json.encode(summary));
            await prefs.setString('summary_last_slot', currentSlot);
          }
        } else {
          await prefs.setString('summary_cached_data', json.encode(summary));
          await prefs.setString('summary_last_slot', currentSlot);
        }

        setState(() {
          _summaryData = summary;
          _extractConversationsFromSummary(summary);
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load conversation summary: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _extractConversationsFromSummary(Map<String, dynamic> summary) {
    List<Map<String, dynamic>> conversationList = [];
    
    if (summary.containsKey('summary') && summary['summary'] is Map<String, dynamic>) {
      final summaryContent = summary['summary'] as Map<String, dynamic>;
      
      // Add main theme
      if (summaryContent.containsKey('main_theme')) {
        conversationList.add({
          'fileName': 'What They Talked About',
          'content': summaryContent['main_theme'].toString(),
          'type': 'theme',
          'fullData': {'main_theme': summaryContent['main_theme']},
        });
      }
      
      // Add child's interaction
      if (summaryContent.containsKey('childs_interaction')) {
        conversationList.add({
          'fileName': 'How Your Child Participated',
          'content': summaryContent['childs_interaction'].toString(),
          'type': 'interaction',
          'fullData': {'childs_interaction': summaryContent['childs_interaction']},
        });
      }
      
      // Add emotional tone
      if (summaryContent.containsKey('emotional_tone') && summaryContent['emotional_tone'] is List) {
        final emotionalTone = summaryContent['emotional_tone'] as List;
        conversationList.add({
          'fileName': 'Conversation Mood',
          'content': emotionalTone.join(', '),
          'type': 'emotion',
          'fullData': {'emotional_tone': emotionalTone},
        });
      }
      
      // Add key points as highlights
      if (summaryContent.containsKey('key_points') && summaryContent['key_points'] is List) {
        final keyPoints = summaryContent['key_points'] as List;
        for (int i = 0; i < keyPoints.length; i++) {
          conversationList.add({
            'fileName': 'Highlight ${i + 1}',
            'content': keyPoints[i].toString(),
            'type': 'highlight',
            'fullData': {'key_point': keyPoints[i], 'index': i + 1},
          });
        }
      }
      
      // Add characters (who was involved)
      if (summaryContent.containsKey('characters') && summaryContent['characters'] is List) {
        final characters = summaryContent['characters'] as List;
        conversationList.add({
          'fileName': 'Who Was Involved',
          'content': characters.join(', '),
          'type': 'characters',
          'fullData': {'characters': characters},
        });
      }
    }
    
    _conversations = conversationList;
  }

  void _logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_api_key');
    
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  void _openConversationDetail(Map<String, dynamic> conversation) {
    final fileName = conversation['fileName'] ?? '';
    final content = conversation['content'] ?? '';
    final type = conversation['type'] ?? 'general';
    final fullData = conversation['fullData'] as Map<String, dynamic>? ?? {};
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConversationDetailWithSummary(
          fileName: fileName,
          content: content,
          type: type,
          fullData: fullData,
          summaryData: _summaryData,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasApiKey && !_isLoading) {
      return NoApiKeyScreen(onLogout: _logout);
    }

    if (_hasApiKey && !_hasActiveSubscription && !_isLoading && _userId != null) {
      return NoSubscriptionScreen(
        onLogout: _logout,
        userId: _userId!,
        onSubscriptionFound: () {
          // Refresh the page when subscription is found
          _checkApiKeyAndLoadData();
        },
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Conversation Summary', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: const Color(0xFF8B5CF6),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [const Color(0xFFF3E8FF), Colors.white],
          ),
        ),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Color(0xFF8B5CF6)),
            SizedBox(height: 16),
            Text("Loading your child's conversation summary...", 
              style: TextStyle(color: Color(0xFF6B7280), fontWeight: FontWeight.w500))
          ],
        )
      );
    }
    
    if (_errorMessage != null) {
      return _buildErrorView();
    }
    
    return _buildConversationsList();
  }

  Widget _buildErrorView() {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: _getCardMaxWidth(context) ?? double.infinity),
        margin: _getResponsivePadding(context),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFFFEF2F2),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFFEE2E2),
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(Icons.error_outline, color: Color(0xFFEF4444), size: 48),
            ),
            const SizedBox(height: 24),
            Text(
              _errorMessage ?? 'Something went wrong',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xFFB91C1C),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Please try again later or contact support if the issue persists.',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xFF6B7280),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationsList() {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: _getCardMaxWidth(context) ?? double.infinity),
        child: SingleChildScrollView(
          padding: _getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSummaryHeader(),
              const SizedBox(height: 24),
              if (_conversations.isNotEmpty) ...[
                ..._conversations.map((conversation) => _buildConversationCard(conversation)),
              ] else
                _buildEmptyState(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryHeader() {
    if (_summaryData == null || !_summaryData!.containsKey('summary')) {
      return const SizedBox.shrink();
    }

    final summary = _summaryData!['summary'] as Map<String, dynamic>;
    final summaryTitle = summary['summary_title'] ?? 'Your Child\'s Conversation';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E7FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.child_care, color: Color(0xFF4F46E5), size: 32),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    summaryTitle,
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Color(0xFF374151)),
                  ),
                ),
              ],
            ),
            if (summary.containsKey('main_theme')) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF9FAFB),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.lightbulb_outline, color: Color(0xFF8B5CF6), size: 18),
                        SizedBox(width: 8),
                        Text(
                          'Main Topic:',
                          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Color(0xFF374151)),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      summary['main_theme'].toString(),
                      style: const TextStyle(fontSize: 15, color: Color(0xFF4B5563), height: 1.5),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          const Text("No conversation summary available yet", 
            style: TextStyle(fontSize: 16, color: Color(0xFF6B7280))),
        ],
      ),
    );
  }

  Widget _buildConversationCard(Map<String, dynamic> conversation) {
    final fileName = conversation['fileName'] ?? '';
    final content = conversation['content'] ?? '';
    final type = conversation['type'] ?? 'general';
    
    IconData typeIcon;
    Color typeColor;
    
    switch (type) {
      case 'theme':
        typeIcon = Icons.topic;
        typeColor = const Color(0xFF8B5CF6);
        break;
      case 'highlight':
        typeIcon = Icons.star;
        typeColor = const Color(0xFFF59E0B);
        break;
      case 'interaction':
        typeIcon = Icons.psychology;
        typeColor = const Color(0xFF10B981);
        break;
      case 'characters':
        typeIcon = Icons.people;
        typeColor = const Color(0xFF3B82F6);
        break;
      case 'emotion':
        typeIcon = Icons.sentiment_satisfied;
        typeColor = const Color(0xFFEC4899);
        break;
      default:
        typeIcon = Icons.chat;
        typeColor = const Color(0xFF6B7280);
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.shade200),
        ),
        child: InkWell(
          onTap: () => _openConversationDetail(conversation),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: typeColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(typeIcon, size: 20, color: typeColor),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        fileName, 
                        style: const TextStyle(
                          fontWeight: FontWeight.bold, 
                          fontSize: 16,
                          color: Color(0xFF374151)
                        )
                      ),
                    ),
                    const Icon(Icons.arrow_forward_ios, size: 16, color: Color(0xFF9CA3AF)),
                  ],
                ),
                if (content.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    content.length > 120 ? '${content.substring(0, 120)}...' : content,
                    style: const TextStyle(
                      fontSize: 15, 
                      color: Color(0xFF4B5563),
                      height: 1.5
                    )
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Simplified conversation detail screen
class ConversationDetailWithSummary extends StatelessWidget {
  final String fileName;
  final String content;
  final String type;
  final Map<String, dynamic> fullData;
  final Map<String, dynamic>? summaryData;

  const ConversationDetailWithSummary({
    super.key,
    required this.fileName,
    required this.content,
    required this.type,
    required this.fullData,
    this.summaryData,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(fileName, style: const TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: const Color(0xFF8B5CF6),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [const Color(0xFFF3E8FF), Colors.white],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: _buildMainCard(),
        ),
      ),
    );
  }

  Widget _buildMainCard() {
    IconData typeIcon;
    Color typeColor;
    
    switch (type) {
      case 'theme':
        typeIcon = Icons.topic;
        typeColor = const Color(0xFF8B5CF6);
        break;
      case 'highlight':
        typeIcon = Icons.star;
        typeColor = const Color(0xFFF59E0B);
        break;
      case 'interaction':
        typeIcon = Icons.psychology;
        typeColor = const Color(0xFF10B981);
        break;
      case 'characters':
        typeIcon = Icons.people;
        typeColor = const Color(0xFF3B82F6);
        break;
      case 'emotion':
        typeIcon = Icons.sentiment_satisfied;
        typeColor = const Color(0xFFEC4899);
        break;
      default:
        typeIcon = Icons.chat;
        typeColor = const Color(0xFF6B7280);
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: typeColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(typeIcon, color: typeColor, size: 32),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    fileName,
                    style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Color(0xFF374151)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF9FAFB),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                content.isNotEmpty ? content : "No details available",
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF4B5563),
                  height: 1.6,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}