import 'dart:async';
import 'package:flutter/material.dart';

// Stub version of NotificationService for Windows build compatibility
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Subscription expiry notification IDs (100-199)
  static const int subscriptionExpiryBaseId = 100;
  static const int maxExpiryNotifications = 12; // Send for 24 hours (every 2 hours)

  // Update alert notification ID (200)
  static const int updateAlertId = 200;

  // Initialize notification service (stub for Windows build)
  Future<void> init() async {
    try {
      debugPrint('NotificationService initialized successfully (stub version for Windows)');
    } catch (e) {
      debugPrint('Error initializing NotificationService: $e');
    }
  }

  // Add new method to request permissions (stub)
  Future<bool> requestNotificationPermissions() async {
    debugPrint('Notification permissions requested (stub)');
    return true; // Always return true for stub
  }

  // Add method to check if notifications are enabled (stub)
  Future<bool> areNotificationsEnabled() async {
    debugPrint('Checking notification status (stub)');
    return true; // Always return true for stub
  }

  // Schedule subscription expiry notifications (stub)
  Future<void> scheduleSubscriptionExpiryNotifications(String userId) async {
    debugPrint('Scheduling subscription expiry notifications for user $userId (stub)');
  }

  // Cancel subscription expiry notifications (stub)
  Future<void> cancelSubscriptionExpiryNotifications() async {
    debugPrint('Cancelling subscription expiry notifications (stub)');
  }

  // Schedule daily update alert notification (stub)
  Future<void> scheduleUpdateAlertNotification(String userId, {TimeOfDay? preferredTime}) async {
    debugPrint('Scheduling update alert notification for user $userId (stub)');
  }

  // Show immediate notification (stub)
  Future<void> showImmediateNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    debugPrint('Showing immediate notification: $title - $body (stub)');
  }

  // Cancel update alert notification (stub)
  Future<void> cancelUpdateAlertNotifications() async {
    debugPrint('Cancelling update alert notifications (stub)');
  }
}
