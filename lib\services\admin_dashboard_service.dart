import 'package:flutter/foundation.dart';
import 'backend_api_service.dart';
import 'package:uuid/uuid.dart';
import 'dart:math';

class AdminDashboardService {
  static final _backendApi = BackendApiService();
  static const _uuid = Uuid();

  // Helper method to check if assigned_at column exists
  static Future<bool> _hasAssignedAtColumn() async {
    if (kIsWeb) return true; // Assume column exists on web
    
    try {
      final columns = await BackendApiService.executeQuery(
        "SHOW COLUMNS FROM SerialNumbers LIKE 'assigned_at'");
      return columns.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking column: $e');
      return false;
    }
  }

  // Get all registered users with their QR code status
  static Future<List<Map<String, dynamic>>> getRegisteredUsers() async {
    try {
      final hasAssignedAt = await _hasAssignedAtColumn();
      
      final query = hasAssignedAt ? '''
        SELECT 
          u.id, 
          u.name, 
          u.email, 
          u.created_at,
          s.serial as qr_code,
          s.status as qr_status,
          s.assigned_at as qr_assigned_date
        FROM Users u
        LEFT JOIN SerialNumbers s ON u.id = s.user_id AND s.status = 'assigned'
        ORDER BY u.created_at DESC
      ''' : '''
        SELECT 
          u.id, 
          u.name, 
          u.email, 
          u.created_at,
          s.serial as qr_code,
          s.status as qr_status,
          s.created_at as qr_assigned_date
        FROM Users u
        LEFT JOIN SerialNumbers s ON u.id = s.user_id AND s.status = 'assigned'
        ORDER BY u.created_at DESC
      ''';
      
      final results = await BackendApiService.executeQuery(query);

      // Check subscription status for each user
      for (var user in results) {
        if (user['id'] != null) {
          final hasActiveSubscription = await isUserSubscriptionActive(user['id']);
          user['has_active_subscription'] = hasActiveSubscription;
        } else {
          user['has_active_subscription'] = false;
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error fetching registered users: $e');
      throw Exception('Failed to fetch registered users: $e');
    }
  }

  // Search users by email or name
  static Future<List<Map<String, dynamic>>> searchUsers(String query) async {
    try {
      final hasAssignedAt = await _hasAssignedAtColumn();
      
      final sqlQuery = hasAssignedAt ? '''
        SELECT 
          u.id, 
          u.name, 
          u.email, 
          u.created_at,
          s.serial as qr_code,
          s.status as qr_status,
          s.assigned_at as qr_assigned_date
        FROM Users u
        LEFT JOIN SerialNumbers s ON u.id = s.user_id
        WHERE u.email LIKE ? OR u.name LIKE ?
        ORDER BY u.created_at DESC
      ''' : '''
        SELECT 
          u.id, 
          u.name, 
          u.email, 
          u.created_at,
          s.serial as qr_code,
          s.status as qr_status,
          s.created_at as qr_assigned_date
        FROM Users u
        LEFT JOIN SerialNumbers s ON u.id = s.user_id
        WHERE u.email LIKE ? OR u.name LIKE ?
        ORDER BY u.created_at DESC
      ''';
      
      return await BackendApiService.executeQuery(
        sqlQuery, 
        [query, query].map((s) => '%$s%').toList()
      );
    } catch (e) {
      debugPrint('Error searching users: $e');
      throw Exception('Failed to search users: $e');
    }
  }

  // Generate new serial numbers
  static Future<List<String>> generateSerialNumbers(int count) async {
    try {
      final List<String> serials = [];

      for (int i = 0; i < count; i++) {
        final serial = 'IMMY-${DateTime.now().year}-${_generateRandomString(6)}';
        serials.add(serial);
      }

      // Insert all serials in a single batch
      await BackendApiService.createQRCodes(serials);

      return serials;
    } catch (e) {
      debugPrint('Error generating serial numbers: $e');
      throw Exception('Failed to generate serial numbers: $e');
    }
  }

  // Get all QR codes with their status
  static Future<List<Map<String, dynamic>>> getAllQRCodes() async {
    try {
      final hasAssignedAt = await _hasAssignedAtColumn();
      
      final sqlQuery = hasAssignedAt ? '''
        SELECT 
          s.id,
          s.serial,
          s.status,
          s.created_at,
          s.assigned_at,
          s.user_id,
          u.name as assigned_to_name,
          u.email as assigned_to_email
        FROM SerialNumbers s
        LEFT JOIN Users u ON s.user_id = u.id
        ORDER BY s.created_at DESC
      ''' : '''
        SELECT 
          s.id,
          s.serial,
          s.status,
          s.created_at,
          s.user_id,
          u.name as assigned_to_name,
          u.email as assigned_to_email
        FROM SerialNumbers s
        LEFT JOIN Users u ON s.user_id = u.id
        ORDER BY s.created_at DESC
      ''';
      
      return await BackendApiService.executeQuery(sqlQuery);
    } catch (e) {
      debugPrint('Error fetching all QR codes: $e');
      throw Exception('Failed to fetch all QR codes: $e');
    }
  }

  // Get available QR codes
  static Future<List<Map<String, dynamic>>> getAvailableQRCodes() async {
    try {
      return await BackendApiService.getAvailableQRCodes();
    } catch (e) {
      debugPrint('Error fetching available QR codes: $e');
      throw Exception('Failed to fetch available QR codes: $e');
    }
  }

  // Assign QR code to user
  static Future<bool> assignQRCodeToUser(dynamic userId, String qrCode) async {
    try {
      // Convert userId to int if it's a string
      final int userIdInt = userId is String ? int.parse(userId) : userId;
      
      // First check if QR code is available
      final qrCodes = await BackendApiService.executeQuery('''
        SELECT id FROM SerialNumbers 
        WHERE serial = ? AND user_id IS NULL AND status = 'active'
      ''', [qrCode]);

      if (qrCodes.isEmpty) {
        throw Exception('QR code is not available for assignment');
      }

      // Get the QR code ID
      final qrCodeId = qrCodes.first['id'];

      // Assign the QR code to the user
      await BackendApiService.assignQRCodeToUser(qrCodeId, userIdInt);

      return true;
    } catch (e) {
      debugPrint('Error assigning QR code: $e');
      throw Exception('Failed to assign QR code: $e');
    }
  }

  // Get user's QR code details
  static Future<Map<String, dynamic>?> getUserQRCode(dynamic userId) async {
    try {
      // Convert userId to int if it's a string
      final int userIdInt = userId is String ? int.parse(userId) : userId;
      
      final hasAssignedAt = await _hasAssignedAtColumn();
      
      final query = hasAssignedAt ? '''
        SELECT 
          s.id,
          s.serial,
          s.status,
          s.created_at,
          s.assigned_at,
          u.name as assigned_to_name,
          u.email as assigned_to_email
        FROM SerialNumbers s
        LEFT JOIN Users u ON s.user_id = u.id
        WHERE s.user_id = ? AND s.status = 'assigned'
      ''' : '''
        SELECT 
          s.id,
          s.serial,
          s.status,
          s.created_at,
          u.name as assigned_to_name,
          u.email as assigned_to_email
        FROM SerialNumbers s
        LEFT JOIN Users u ON s.user_id = u.id
        WHERE s.user_id = ? AND s.status = 'assigned'
      ''';
      
      final results = await BackendApiService.executeQuery(query, [userIdInt]);

      return results.isEmpty ? null : results.first;
    } catch (e) {
      debugPrint('Error fetching user QR code: $e');
      throw Exception('Failed to fetch user QR code: $e');
    }
  }

  // Update user profile
  static Future<void> updateUserProfile(dynamic userId, String name, String email) async {
    try {
      // Convert userId to int if it's a string
      final int userIdInt = userId is String ? int.parse(userId) : userId;
      
      await BackendApiService.executeQuery('''
        UPDATE Users 
        SET name = ?, email = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      ''', [name, email, userIdInt]);
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      throw Exception('Failed to update user profile: $e');
    }
  }

  // Set user admin status
  static Future<void> setUserAdmin(dynamic userId, bool isAdmin) async {
    try {
      // Convert userId to int if it's a string
      final int userIdInt = userId is String ? int.parse(userId) : userId;
      
      await BackendApiService.executeQuery('''
        UPDATE Users 
        SET is_admin = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      ''', [isAdmin ? 1 : 0, userIdInt]);
    } catch (e) {
      debugPrint('Error setting user admin status: $e');
      throw Exception('Failed to set user admin status: $e');
    }
  }

  // Change user password
  static Future<void> changeUserPassword(dynamic userId, String newPassword) async {
    try {
      // Convert userId to int if it's a string
      final int userIdInt = userId is String ? int.parse(userId) : userId;
      
      await BackendApiService.executeQuery('''
        UPDATE Users 
        SET password = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      ''', [newPassword, userIdInt]);
    } catch (e) {
      debugPrint('Error changing user password: $e');
      throw Exception('Failed to change user password: $e');
    }
  }

  // Helper method to generate random string for serial numbers
  static String _generateRandomString(int length) {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    final random = Random();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  // Add this method to check subscription status for a user
  static Future<bool> isUserSubscriptionActive(int userId) async {
    try {
      // Skip subscription check for admin users (ID 0 or negative)
      if (userId <= 0) {
        return false;
      }

      final subscriptions = await BackendApiService.executeQuery(
        'SELECT * FROM Subscriptions WHERE user_id = ? AND status = "active" AND end_date > NOW()',
        [userId]
      );
      return subscriptions.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking subscription status: $e');
      return false;
    }
  }

  // Get all subscriptions with user details
  static Future<List<Map<String, dynamic>>> getAllSubscriptions() async {
    try {
      final sqlQuery = '''
        SELECT
          s.id as subscription_id,
          s.user_id,
          s.serial_id,
          s.start_date,
          s.end_date,
          s.status,
          s.stripe_subscription_id,
          s.stripe_price_id,
          s.start_date as subscription_created_at,
          COALESCE(u.name, 'Unknown User') as user_name,
          COALESCE(u.email, 'No Email') as user_email,
          COALESCE(sn.serial, 'No Serial') as serial_number
        FROM Subscriptions s
        LEFT JOIN Users u ON s.user_id = u.id AND u.id > 0
        LEFT JOIN SerialNumbers sn ON s.serial_id = sn.id
        WHERE s.user_id > 0
        ORDER BY s.start_date DESC
      ''';

      return await BackendApiService.executeQuery(sqlQuery);
    } catch (e) {
      debugPrint('Error getting all subscriptions: $e');
      // Return empty list instead of throwing exception to prevent dashboard crash
      return [];
    }
  }

  // Cancel a subscription
  static Future<void> cancelSubscription(int subscriptionId) async {
    try {
      await BackendApiService.executeQuery('''
        UPDATE Subscriptions
        SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      ''', [subscriptionId]);

      debugPrint('Subscription $subscriptionId cancelled successfully');
    } catch (e) {
      debugPrint('Error cancelling subscription: $e');
      throw Exception('Failed to cancel subscription: $e');
    }
  }

  // Update getAllUsers to include subscription status
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final sqlQuery = '''
        SELECT
          u.id,
          u.name,
          u.email,
          u.is_admin,
          u.created_at,
          s.serial as qr_code,
          s.status as qr_status,
          s.created_at as qr_assigned_date
        FROM Users u
        LEFT JOIN SerialNumbers s ON u.id = s.user_id
        ORDER BY u.created_at DESC
      ''';

      final users = await BackendApiService.executeQuery(sqlQuery);

      // Check subscription status for each user
      for (var user in users) {
        if (user['id'] != null) {
          final hasActiveSubscription = await isUserSubscriptionActive(user['id']);
          user['has_active_subscription'] = hasActiveSubscription;
        } else {
          user['has_active_subscription'] = false;
        }
      }
      
      return users;
    } catch (e) {
      debugPrint('Error fetching all users: $e');
      throw Exception('Failed to fetch all users: $e');
    }
  }
}
