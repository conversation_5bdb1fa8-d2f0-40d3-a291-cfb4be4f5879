import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../widgets/subscription_banner.dart';
import '../widgets/flowise_chatbot_popup.dart';
import '../services/serial_service.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart' as admin_auth;
import '../services/users_auth_service.dart' as user_auth;
import '../services/subscription_monitor_service.dart';
import '../services/notification_service.dart';
import '../services/payment_processor.dart'; // <-- Imported PaymentProcessor here
import 'insights_page.dart';
import 'coach_page.dart';
import 'payments_page.dart';
import 'settings_page.dart';

// SetupHelper class
class SetupHelper {
  static Future<bool> isSetupCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('immy_setup_completed') ?? false;
    } catch (e) {
      print('Error checking setup status: $e');
      return false;
    }
  }

  static Future<void> markSetupCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('immy_setup_completed', true);
    } catch (e) {
      print('Error marking setup as completed: $e');
    }
  }

  static Future<void> resetSetupStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('immy_setup_completed');
    } catch (e) {
      print('Error resetting setup status: $e');
    }
  }
}

class HomePage extends StatefulWidget {
  final SerialService serialService;
  final ApiService apiService;
  final admin_auth.AuthService? authService;
  final user_auth.AuthService? usersAuthService;

  const HomePage({
    Key? key,
    required this.serialService,
    required this.apiService,
    this.authService,
    this.usersAuthService,
  }) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  int _selectedIndex = 0;

  // Setter with validation
  set selectedIndex(int index) {
    debugPrint('HomePage: Setting selectedIndex to $index, pages length: ${_pages.length}');
    if (index >= 0 && index < _pages.length) {
      _selectedIndex = index;
    } else {
      debugPrint('HomePage: Invalid index $index, keeping current index $_selectedIndex');
    }
  }
  bool _isAdmin = false;
  bool _isLoading = true;
  String _userName = '';
  final SubscriptionMonitorService _subscriptionMonitor = SubscriptionMonitorService();
  int _userId = 0;

  // Subscription status caching
  bool? _cachedSubscriptionStatus;
  DateTime? _cachedNextPaymentDate;
  DateTime? _lastSubscriptionCheck;

  // Getter for pages that rebuilds with current state
  List<Widget> get _pages => [
    HomeContent(
      getSubscriptionStatus: _getSubscriptionStatus,
      getNextPaymentDate: _getNextPaymentDate,
      cachedSubscriptionStatus: _cachedSubscriptionStatus,
      cachedNextPaymentDate: _cachedNextPaymentDate,
    ),
    InsightsPage(apiService: widget.apiService, userId: _userId),
    CoachPage(apiService: widget.apiService, userId: _userId),
    const PaymentsPage(),
    const SettingsPage(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _checkAdminStatus();
    _loadUserData();

    // Handle initial tab if provided
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map<String, dynamic> && args.containsKey('initialTab')) {
        final initialTab = args['initialTab'] as int;
        debugPrint('HomePage: Initial tab requested: $initialTab, pages length: ${_pages.length}');
        if (initialTab >= 0 && initialTab < _pages.length) {
          setState(() {
            _selectedIndex = initialTab;
          });
        } else {
          debugPrint('HomePage: Invalid initial tab $initialTab, using 0');
        }
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // Stop subscription monitoring when the page is disposed
    _subscriptionMonitor.stopMonitoring();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed && _selectedIndex == 0) {
      // Refresh subscription status when app resumes and user is on home tab
      _refreshSubscriptionStatus();
    }
  }

  Future<void> _loadUserData() async {
    setState(() => _isLoading = true);

    try {
      // Try to get user from users auth service first
      if (widget.usersAuthService != null) {
        final user = await widget.usersAuthService!.getCurrentUser();
        if (user != null) {
          setState(() {
            _userName = user.name;
            _isAdmin = user.isAdmin;
            _userId = user.id;
            _isLoading = false;
          });
          
          // Start subscription monitoring - convert to string when needed
          _subscriptionMonitor.startMonitoring(_userId.toString());
          
          // Schedule daily update notification - convert to string when needed
          _subscriptionMonitor.scheduleDailyUpdateNotification(_userId.toString());
          
          return;
        }
      }

      // Fall back to admin auth service
      if (widget.authService != null) {
        final user = await widget.authService!.getCurrentUser();
        if (mounted && user != null) {
          setState(() {
            _userName = user.name;
            try {
              _userId = int.parse(user.id); // Safely convert string ID to int
            } catch (e) {
              print('Error converting user ID: $e');
              _userId = 0; // Fallback value
            }
            _isLoading = false;
          });
          
          // Start subscription monitoring - convert to string when needed
          _subscriptionMonitor.startMonitoring(_userId.toString());
          
          // Schedule daily update notification - convert to string when needed
          _subscriptionMonitor.scheduleDailyUpdateNotification(_userId.toString());
        }
      }
    } catch (e) {
      print('Error loading user: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _checkAdminStatus() async {
    try {
      // Try users auth service first
      if (widget.usersAuthService != null) {
        final isAdmin = await widget.usersAuthService!.isCurrentUserAdmin();
        if (mounted) {
          setState(() => _isAdmin = isAdmin);
        }
        return;
      }

      // Fall back to admin auth service
      if (widget.authService != null) {
        final isAdmin = await widget.authService!.isCurrentUserAdmin();
        if (mounted) {
          setState(() => _isAdmin = isAdmin);
        }
      }
    } catch (e) {
      print('Error checking admin status: $e');
    }
  }

  Future<void> _logout() async {
    try {
      // Logout from both services
      await widget.usersAuthService?.logout();
      await widget.authService?.logout();

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Logout error: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return Stack(
      children: [
        Scaffold(
          backgroundColor: isDark ? const Color(0xFF181A20) : Colors.white,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: isDark ? const Color(0xFF23272F) : const Color(0xFF8B5CF6),
            title: const Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.white,
                  child: Text(
                    'IA',
                    style: TextStyle(
                      color: Color(0xFF8B5CF6),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'Immy App',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined, color: Colors.white),
                onPressed: () {},
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.account_circle, color: Colors.white),
                onSelected: (value) {
                  if (value == 'logout') _logout();
                  if (value == 'settings') setState(() => _selectedIndex = 4);
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'profile',
                    child: Row(
                      children: [
                        const Icon(Icons.person, color: Color(0xFF8B5CF6)),
                        const SizedBox(width: 8),
                        Text(_isLoading ? 'Loading...' : 'Hi, $_userName'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings, color: Color(0xFF8B5CF6)),
                        SizedBox(width: 8),
                        Text('Settings'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Color(0xFF8B5CF6)),
                        SizedBox(width: 8),
                        Text('Logout'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          drawer: _buildDrawer(context),
          body: _selectedIndex >= 0 && _selectedIndex < _pages.length
              ? _pages[_selectedIndex]
              : _pages[0], // Fallback to home page
          bottomNavigationBar: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: BottomNavigationBar(
                currentIndex: _selectedIndex,
                onTap: (index) {
                  // Safeguard against invalid index
                  if (index >= 0 && index < _pages.length) {
                    setState(() {
                      _selectedIndex = index;
                      // Refresh subscription status when returning to home tab
                      if (index == 0) {
                        _refreshSubscriptionStatus();
                      }
                    });
                  }
                },
                type: BottomNavigationBarType.fixed,
                selectedItemColor: const Color(0xFF8B5CF6),
                unselectedItemColor: isDark ? Colors.grey[400] : const Color(0xFF6B7280),
                elevation: 0,
                backgroundColor: isDark ? const Color(0xFF23272F) : Colors.white,
                items: const [
                  BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
                  BottomNavigationBarItem(icon: Icon(Icons.chat_bubble_outline), label: 'Insights'),
                  BottomNavigationBarItem(icon: Icon(Icons.school), label: 'Coach'),
                  BottomNavigationBarItem(icon: Icon(Icons.payment), label: 'Payments'),
                  BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'Settings'),
                ],
              ),
            ),
          ),
        ),
        // Floating chat button (moved higher)
        Positioned(
          bottom: 90,
          right: 24,
          child: FloatingActionButton.extended(
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => const FlowiseChatbotPanel(),
              );
            },
            backgroundColor: const Color(0xFF8B5CF6),
            icon: const Icon(Icons.chat_bubble_outline, color: Colors.white),
            label: const Text(
              'Support',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 8,
          ),
        ),
      ],
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(color: Color(0xFF8B5CF6)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CircleAvatar(
                  backgroundColor: Colors.white,
                  radius: 30,
                  child: Text(
                    'IA',
                    style: TextStyle(
                      color: Color(0xFF8B5CF6),
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _isLoading ? 'Loading...' : _userName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                Text(
                  _isAdmin ? 'Admin Account' : 'User Account',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text('Home'),
            onTap: () => _updateIndex(0),
          ),
          // Setup option - always visible
          FutureBuilder<bool>(
            future: SetupHelper.isSetupCompleted(),
            builder: (context, snapshot) {
              final isSetupCompleted = snapshot.data ?? false;
              return ListTile(
                leading: Icon(
                  isSetupCompleted ? Icons.settings : Icons.settings_suggest,
                  color: isSetupCompleted ? Colors.green : Colors.orange,
                ),
                title: Text(isSetupCompleted ? 'Setup Immy' : 'Complete Setup'),
                subtitle: Text(isSetupCompleted ? 'Reconfigure your Immy' : 'Setup required'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/setup');
                },
              );
            },
          ),
          // Only show serial management for admins
          if (_isAdmin) ...[
            ListTile(
              leading: const Icon(Icons.qr_code),
              title: const Text('Serial Management'),
              onTap: () => Navigator.pushNamed(context, '/serial-management'),
            ),
            ListTile(
              leading: const Icon(Icons.search),
              title: const Text('Serial Lookup'),
              onTap: () => Navigator.pushNamed(context, '/serial-lookup'),
            ),
          ],
          // Admin section
          if (_isAdmin) ...[
            const Divider(),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                'Administration',
                style: TextStyle(
                  color: Color(0xFF6B7280),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.admin_panel_settings),
              title: const Text('Admin Dashboard'),
              onTap: () => Navigator.pushNamed(context, '/admin/dashboard'),
            ),
          ],
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Logout'),
            onTap: _logout,
          ),
        ],
      ),
    );
  }

  void _updateIndex(int index) {
    Navigator.pop(context);
    // Safeguard against invalid index
    if (index >= 0 && index < _pages.length) {
      setState(() {
        _selectedIndex = index;
        // Refresh subscription status when returning to home tab
        if (index == 0) {
          _refreshSubscriptionStatus();
        }
      });
    }
  }

  // Method to check subscription status with caching
  Future<bool> _getSubscriptionStatus() async {
    final now = DateTime.now();

    // If we have a cached status and it's less than 30 seconds old, use it
    if (_cachedSubscriptionStatus != null &&
        _lastSubscriptionCheck != null &&
        now.difference(_lastSubscriptionCheck!).inSeconds < 30) {
      return _cachedSubscriptionStatus!;
    }

    // Otherwise, fetch fresh status
    try {
      final isActive = await PaymentProcessor.verifyActiveSubscription(_userId);
      _cachedSubscriptionStatus = isActive;
      _lastSubscriptionCheck = now;
      return isActive;
    } catch (e) {
      debugPrint('Error checking subscription status: $e');
      // Return cached status if available, otherwise false
      return _cachedSubscriptionStatus ?? false;
    }
  }

  // Method to get next payment date with caching
  Future<DateTime?> _getNextPaymentDate() async {
    final now = DateTime.now();

    // If we have a cached date and it's less than 5 minutes old, use it
    if (_cachedNextPaymentDate != null &&
        _lastSubscriptionCheck != null &&
        now.difference(_lastSubscriptionCheck!).inMinutes < 5) {
      return _cachedNextPaymentDate;
    }

    // Otherwise, fetch fresh date
    try {
      final nextDate = await PaymentProcessor.getNextPaymentDate(_userId);
      _cachedNextPaymentDate = nextDate;
      return nextDate;
    } catch (e) {
      debugPrint('Error getting next payment date: $e');
      return _cachedNextPaymentDate;
    }
  }

  // Method to refresh subscription status (call this when returning from payment pages)
  void _refreshSubscriptionStatus() {
    setState(() {
      _cachedSubscriptionStatus = null;
      _cachedNextPaymentDate = null;
      _lastSubscriptionCheck = null;
    });
  }
}

class HomeContent extends StatelessWidget {
  final Future<bool> Function() getSubscriptionStatus;
  final Future<DateTime?> Function() getNextPaymentDate;
  final bool? cachedSubscriptionStatus;
  final DateTime? cachedNextPaymentDate;

  const HomeContent({
    super.key,
    required this.getSubscriptionStatus,
    required this.getNextPaymentDate,
    this.cachedSubscriptionStatus,
    this.cachedNextPaymentDate,
  });

  Widget _buildQuickActionCard(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    Color backgroundColor,
    Color iconColor, {
    required VoidCallback onTap,
  }) {
    // Get screen width to calculate card size
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360; // Extra small screens
    
    return Card(
      elevation: 2, // Reduced elevation for better mobile appearance
      shadowColor: backgroundColor.withOpacity(0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: EdgeInsets.all(isSmallScreen ? 12 : 14), // Reduced padding for small screens
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                backgroundColor,
                backgroundColor.withOpacity(0.7),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(
                icon,
                size: isSmallScreen ? 24 : 28, // Smaller icon for small screens
                color: iconColor,
              ),
              const SizedBox(height: 8), // Reduced spacing
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: isSmallScreen ? 14 : 15, // Smaller text for small screens
                  color: iconColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2), // Reduced spacing
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: isSmallScreen ? 10 : 11, // Smaller text for small screens
                  color: iconColor.withOpacity(0.8),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get access to the parent state to check admin status and userId
    final homePageState = context.findAncestorStateOfType<_HomePageState>();
    final isAdmin = homePageState?._isAdmin ?? false;
    final userId = homePageState?._userId ?? 0;
    
    // Get screen dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hero banner with gradient background
            Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 16.0 : 20.0,
                  vertical: isSmallScreen ? 12.0 : 16.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'What would you like to do today?',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 18 : 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    FutureBuilder<bool>(
                      future: getSubscriptionStatus(),
                      builder: (context, snapshot) {
                        // Show loading state only if we don't have cached data
                        if (snapshot.connectionState == ConnectionState.waiting && cachedSubscriptionStatus == null) {
                          return Container(
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Center(
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                            ),
                          );
                        }

                        final isActive = snapshot.data ?? cachedSubscriptionStatus ?? false;
                        if (!isActive) {
                          return SubscriptionBanner(isActive: false);
                        }

                        // If active, show next payment date
                        return FutureBuilder<DateTime?>(
                          future: getNextPaymentDate(),
                          builder: (context, dateSnapshot) {
                            final nextPaymentDate = dateSnapshot.data ?? cachedNextPaymentDate;
                            String? formattedDate;
                            if (nextPaymentDate != null) {
                              formattedDate = '${nextPaymentDate.year}-${nextPaymentDate.month.toString().padLeft(2, '0')}-${nextPaymentDate.day.toString().padLeft(2, '0')}';
                            }
                            return SubscriptionBanner(
                              isActive: true,
                              nextPaymentDate: formattedDate,
                            );
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            // User profile card
            Padding(
              padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
              child: Card(
                elevation: 2, // Reduced elevation
                shadowColor: Colors.black.withOpacity(0.1),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const CircleAvatar(
                            radius: 24,
                            backgroundColor: Color(0xFFDDEEFD),
                            child: Text(
                              'IB',
                              style: TextStyle(
                                color: Color(0xFF1E40AF),
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Immy Bear',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  'Connected since Jan 2023',
                                  style: TextStyle(
                                    color: const Color(0xFF6B7280),
                                    fontSize: isSmallScreen ? 12 : 13,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const LinearProgressIndicator(
                        value: 0.7,
                        backgroundColor: Color(0xFFE5E7EB),
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8B5CF6)),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      const SizedBox(height: 8),
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Learning progress',
                            style: TextStyle(
                              color: Color(0xFF6B7280),
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '70%',
                            style: TextStyle(
                              color: Color(0xFF8B5CF6),
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Quick actions section with setup integration
            Padding(
              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12.0 : 16.0),
              child: FutureBuilder<bool>(
                future: SetupHelper.isSetupCompleted(),
                builder: (context, snapshot) {
                  final isSetupCompleted = snapshot.data ?? false;
                  
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Quick Actions',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 18 : 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      // Setup status card - always show
                      Card(
                        elevation: 2,
                        shadowColor: (isSetupCompleted ? Colors.green : Colors.orange).withOpacity(0.2),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                        color: isSetupCompleted ? const Color(0xFFF0FDF4) : const Color(0xFFFFF7ED),
                        child: InkWell(
                          onTap: () => Navigator.pushNamed(context, '/setup'),
                          borderRadius: BorderRadius.circular(16),
                          child: Padding(
                            padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 20,
                                  backgroundColor: isSetupCompleted 
                                      ? const Color(0xFFDCFCE7) 
                                      : const Color(0xFFFED7AA),
                                  child: Icon(
                                    isSetupCompleted ? Icons.check_circle : Icons.settings_suggest,
                                    size: 20,
                                    color: isSetupCompleted 
                                        ? const Color(0xFF16A34A) 
                                        : const Color(0xFFD97706),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        isSetupCompleted ? 'Setup Immy' : 'Complete Setup',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        isSetupCompleted 
                                            ? 'Reconfigure your Immy Bear'
                                            : 'Setup your Immy Bear connection',
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 12 : 13,
                                          color: const Color(0xFF6B7280),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios, 
                                  size: 14,
                                  color: isSetupCompleted 
                                      ? const Color(0xFF16A34A) 
                                      : const Color(0xFFD97706),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Regular quick action cards
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: isSmallScreen ? 8 : 12,
                          mainAxisSpacing: isSmallScreen ? 8 : 12,
                          childAspectRatio: isSmallScreen ? 1.1 : 1.2,
                        ),
                        itemCount: 2,
                        itemBuilder: (context, index) {
                          final cards = <Widget>[
                            _buildQuickActionCard(
                              context,
                              Icons.history,
                              'Recent Conversations',
                              'View what has been learning',
                              const Color(0xFFE0E7FF),
                              const Color(0xFF4F46E5),
                              onTap: () => Navigator.pushNamed(
                                context,
                                '/recent-conversations',
                                arguments: {'userId': userId},
                              ),
                            ),

                            _buildQuickActionCard(
                              context,
                              Icons.group_add,
                              'Add Another Immy',
                              'Link a new Immy bear',
                              const Color(0xFFEDE9FE),
                              const Color(0xFF8B5CF6),
                              onTap: () => Navigator.pushNamed(context, '/device-management'),
                            ),
                          ];
                          return cards[index];
                        },
                      ),

            // Admin section
            if (isAdmin)
              Padding(
                padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
                child: Card(
                  elevation: 2,
                  shadowColor: Colors.red.withOpacity(0.2),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                  color: const Color(0xFFFEF2F2),
                  child: InkWell(
                    onTap: () => Navigator.pushNamed(context, '/admin/dashboard'),
                    borderRadius: BorderRadius.circular(16),
                    child: Padding(
                      padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
                      child: Row(
                        children: [
                          const CircleAvatar(
                            radius: 20,
                            backgroundColor: Color(0xFFFEE2E2),
                            child: Icon(
                              Icons.admin_panel_settings,
                              size: 20,
                              color: Color(0xFFDC2626),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Admin Dashboard',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  'Manage serials and users',
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 12 : 13,
                                    color: const Color(0xFF6B7280),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Icon(Icons.arrow_forward_ios, size: 14),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              
            // Add bottom padding for navigation bar
            const SizedBox(height: 20),
          ],
        );}
      ),
    );
  }
}