import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/theme_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';
import '../services/api_service.dart';
import '../services/backend_api_service.dart';
import '../services/history_service.dart';
import '../services/payment_processor.dart';
import 'coach_history_page.dart';
import 'no_api_key_screen.dart';
import 'no_subscription_screen.dart';

class CoachPage extends StatefulWidget {
  final ApiService apiService;
  final int userId;
  
  const CoachPage({Key? key, required this.apiService, required this.userId}) : super(key: key);

  @override
  _CoachPageState createState() => _CoachPageState();
}

class _CoachPageState extends State<CoachPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _coachData;
  String? _errorMessage;
  bool _hasApiKey = false;
  bool _hasActiveSubscription = false;
  final HistoryService _historyService = HistoryService();

  @override
  void initState() {
    super.initState();
    _checkApiKeyAndLoadData();
  }

  bool _isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= 768;
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    if (_isLargeScreen(context)) {
      return const EdgeInsets.symmetric(horizontal: 32, vertical: 24);
    }
    return const EdgeInsets.all(12);
  }

  double? _getCardMaxWidth(BuildContext context) {
    if (_isLargeScreen(context)) {
      return 1000;
    }
    return null;
  }

  String _getCurrentTimeSlot() {
    final now = DateTime.now();
    return now.hour < 12 ? 'AM' : 'PM';
  }

  Future<void> _checkApiKeyAndLoadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final apiKey = prefs.getString('user_api_key');

      if (apiKey == null || apiKey.isEmpty) {
        setState(() {
          _hasApiKey = false;
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _hasApiKey = true;
      });

      // Get user ID from SharedPreferences as fallback if widget.userId is invalid
      int userId = widget.userId;
      if (userId <= 0) {
        final savedUserId = await BackendApiService.getCurrentUserId();
        if (savedUserId != null && savedUserId > 0) {
          userId = savedUserId;
          debugPrint('Using saved user ID from SharedPreferences: $userId');
        } else {
          debugPrint('No valid user ID found, skipping subscription check');
          setState(() {
            _hasActiveSubscription = false;
            _isLoading = false;
          });
          return;
        }
      }

      // Check subscription status
      bool hasActiveSubscription = await PaymentProcessor.verifyActiveSubscription(userId);

      // If no active subscription found, try syncing with Stripe
      if (!hasActiveSubscription) {
        debugPrint('No active subscription found for user $userId, attempting to sync with Stripe...');
        try {
          await PaymentProcessor.syncWithStripe(userId);
          // Re-check subscription status after sync
          hasActiveSubscription = await PaymentProcessor.verifyActiveSubscription(userId);
          debugPrint('After sync, subscription status: $hasActiveSubscription');
        } catch (syncError) {
          debugPrint('Error syncing with Stripe: $syncError');
          // Continue with original subscription status
        }
      }

      setState(() {
        _hasActiveSubscription = hasActiveSubscription;
      });

      if (!hasActiveSubscription) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      await _loadCoachData();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading coaching insights: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCoachData() async {
    if (!_hasApiKey || !_hasActiveSubscription) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final currentSlot = _getCurrentTimeSlot();
      final prefs = await SharedPreferences.getInstance();
      final cachedDataString = prefs.getString('coach_cached_data');
      final lastSlot = prefs.getString('coach_last_slot');

      if (currentSlot == lastSlot && cachedDataString != null) {
        final cachedData = json.decode(cachedDataString) as Map<String, dynamic>;
        setState(() => _coachData = cachedData);
      } else {
        final data = await widget.apiService.getEnhancements();

        if (cachedDataString != null) {
          final cachedData = json.decode(cachedDataString) as Map<String, dynamic>;
          final isEqual = const DeepCollectionEquality().equals(data, cachedData);

          if (!isEqual) {
            await _historyService.saveCoachHistory(data);
          }
        } else {
          await _historyService.saveCoachHistory(data);
        }

        await prefs.setString('coach_cached_data', json.encode(data));
        await prefs.setString('coach_last_slot', currentSlot);

        setState(() => _coachData = data);
      }
    } catch (e) {
      setState(() => _errorMessage = 'Failed to load coaching insights: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_api_key');
    
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasApiKey && !_isLoading) {
      return NoApiKeyScreen(onLogout: _logout);
    }

    if (_hasApiKey && !_hasActiveSubscription && !_isLoading) {
      return NoSubscriptionScreen(
        onLogout: _logout,
        userId: widget.userId,
        onSubscriptionFound: () {
          // Refresh the page when subscription is found
          _checkApiKeyAndLoadData();
        },
      );
    }

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, _) {
        final isDark = themeProvider.isDarkMode;
        final theme = Theme.of(context);
        return Scaffold(
          appBar: AppBar(
            title: const Text('Learning Coach', style: TextStyle(fontWeight: FontWeight.bold)),
            backgroundColor: theme.appBarTheme.backgroundColor,
            foregroundColor: theme.appBarTheme.foregroundColor,
            elevation: 0,
            centerTitle: true,
            actions: [
              IconButton(
                icon: const Icon(Icons.history),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CoachHistoryPage(historyService: _historyService),
                    ),
                  );
                },
                tooltip: 'View History',
              ),
              IconButton(
                icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
                tooltip: isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode',
                onPressed: () => themeProvider.toggleTheme(),
              ),
            ],
          ),
          body: _buildBody(context, isDark, theme),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, bool isDark, ThemeData theme) {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: theme.colorScheme.primary),
            const SizedBox(height: 16),
            Text("Loading your child's learning insights...",
                style: TextStyle(color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7), fontWeight: FontWeight.w500))
          ],
        ),
      );
    }
    
    if (_errorMessage != null) return _buildErrorView(context, isDark, theme);
    
    if (_coachData == null || _coachData!["recommendations"] == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.school_outlined, size: 64, color: isDark ? Colors.grey[700] : Colors.grey[400]),
            const SizedBox(height: 16),
            Text("No learning insights available yet",
                style: TextStyle(fontSize: 16, color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7))),
          ],
        ),
      );
    }
    
    final recommendations = _coachData!["recommendations"] as Map<String, dynamic>;
    
    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        gradient: isDark
            ? null
            : LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [const Color(0xFFF3E8FF), Colors.white],
              ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: _getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildChildProfileHeader(context, recommendations, isDark, theme),
              const SizedBox(height: 20),
              _buildPriorityFocusAreas(context, recommendations, isDark, theme),
              const SizedBox(height: 16),
              _buildAcademicEnhancements(context, recommendations, isDark, theme),
              const SizedBox(height: 16),
              _buildSocialEmotionalEnhancements(context, recommendations, isDark, theme),
              const SizedBox(height: 16),
              _buildCommunicationEnhancements(context, recommendations, isDark, theme),
              const SizedBox(height: 16),
              _buildParentCollaborationTips(context, recommendations, isDark, theme),
              const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChildProfileHeader(BuildContext context, Map<String, dynamic> recommendations, bool isDark, ThemeData theme) {
    final profile = recommendations['child_profile_summary'] as Map<String, dynamic>?;
    if (profile == null) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    return Card(
      elevation: 2,
      color: theme.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 16 : 12),
                  decoration: BoxDecoration(
                    color: isDark ? const Color(0xFF312E81) : const Color(0xFFE0E7FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.child_care,
                    color: isDark ? Colors.purple[200] : const Color(0xFF4F46E5),
                    size: isLarge ? 32 : 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Your Child's Learning Profile",
                        style: TextStyle(
                          fontSize: isLarge ? 20 : 18,
                          fontWeight: FontWeight.bold,
                          color: theme.textTheme.bodyLarge?.color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "Based on recent interactions",
                        style: TextStyle(fontSize: isLarge ? 14 : 12, color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (profile['overview'] != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF23272F) : const Color(0xFFF9FAFB),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: isDark ? const Color(0xFF374151) : Colors.grey.shade200),
                ),
                child: Text(
                  profile['overview'].toString(),
                  style: TextStyle(
                    fontSize: isLarge ? 15 : 14,
                    color: theme.textTheme.bodyMedium?.color,
                    height: 1.5,
                  ),
                ),
              ),
              const SizedBox(height: 12),
            ],
            if (profile['key_positive_attributes_noted'] != null) ...[
              Text(
                "Strengths We've Noticed:",
                style: TextStyle(
                  fontSize: isLarge ? 16 : 14,
                  fontWeight: FontWeight.w600,
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 8),
              ...((profile['key_positive_attributes_noted'] as List?) ?? []).map((attribute) => 
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.star, size: 16, color: isDark ? Colors.amber[200] : Colors.amber[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          attribute.toString(),
                          style: TextStyle(
                            fontSize: isLarge ? 14 : 13,
                            color: theme.textTheme.bodyMedium?.color,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityFocusAreas(BuildContext context, Map<String, dynamic> recommendations, bool isDark, ThemeData theme) {
    final priorityAreas = recommendations['priority_focus_areas_with_rationale'] as List?;
    if (priorityAreas == null || priorityAreas.isEmpty) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    return Card(
      elevation: 1,
      color: theme.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: isDark ? const Color(0xFF065F46) : const Color(0xFFD1FAE5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.flag,
                    color: isDark ? Colors.green[200] : const Color(0xFF059669),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Priority Focus Areas",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...priorityAreas.map((area) => _buildPriorityAreaCard(area, isDark, theme, isLarge)),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityAreaCard(dynamic area, bool isDark, ThemeData theme, bool isLarge) {
    if (area is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(isLarge ? 16 : 14),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF23272F) : const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: isDark ? const Color(0xFF374151) : Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            area['area']?.toString() ?? 'Focus Area',
            style: TextStyle(
              fontSize: isLarge ? 16 : 14,
              fontWeight: FontWeight.w600,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          if (area['rationale'] != null) ...[
            const SizedBox(height: 8),
            Text(
              area['rationale'].toString(),
              style: TextStyle(
                fontSize: isLarge ? 14 : 13,
                color: theme.textTheme.bodyMedium?.color,
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAcademicEnhancements(BuildContext context, Map<String, dynamic> recommendations, bool isDark, ThemeData theme) {
    final academicEnhancements = recommendations['academic_enhancements'] as List?;
    if (academicEnhancements == null || academicEnhancements.isEmpty) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    return Card(
      elevation: 1,
      color: theme.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: isDark ? const Color(0xFF7C2D12) : const Color(0xFFFED7AA),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.school,
                    color: isDark ? Colors.orange[200] : const Color(0xFFEA580C),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Learning Activities",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...academicEnhancements.map((enhancement) => _buildAcademicEnhancementCard(enhancement, isDark, theme, isLarge)),
          ],
        ),
      ),
    );
  }

  Widget _buildAcademicEnhancementCard(dynamic enhancement, bool isDark, ThemeData theme, bool isLarge) {
    if (enhancement is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(isLarge ? 16 : 14),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF23272F) : const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: isDark ? const Color(0xFF374151) : Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            enhancement['skill_area']?.toString() ?? 'Skill Area',
            style: TextStyle(
              fontSize: isLarge ? 16 : 15,
              fontWeight: FontWeight.w600,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          if (enhancement['current_observed_level'] != null) ...[
            const SizedBox(height: 8),
            Text(
              "Current Level: ${enhancement['current_observed_level']}",
              style: TextStyle(
                fontSize: isLarge ? 14 : 13,
                color: theme.textTheme.bodyMedium?.color?.withOpacity(0.8),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          if (enhancement['recommended_activities'] != null) ...[
            const SizedBox(height: 12),
            Text(
              "Suggested Activities:",
              style: TextStyle(
                fontSize: isLarge ? 15 : 14,
                fontWeight: FontWeight.w500,
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            ...((enhancement['recommended_activities'] as List?) ?? []).map((activity) => 
              _buildActivityCard(activity, isDark, theme, isLarge)
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActivityCard(dynamic activity, bool isDark, ThemeData theme, bool isLarge) {
    if (activity is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(isLarge ? 12 : 10),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1F2937) : Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: isDark ? const Color(0xFF4B5563) : Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.play_circle_outline, size: 16, color: isDark ? Colors.blue[200] : Colors.blue[600]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  activity['name']?.toString() ?? 'Activity',
                  style: TextStyle(
                    fontSize: isLarge ? 14 : 13,
                    fontWeight: FontWeight.w500,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
              ),
            ],
          ),
          if (activity['description'] != null) ...[
            const SizedBox(height: 6),
            Text(
              activity['description'].toString(),
              style: TextStyle(
                fontSize: isLarge ? 13 : 12,
                color: theme.textTheme.bodyMedium?.color,
                height: 1.3,
              ),
            ),
          ],
          if (activity['materials_needed'] != null) ...[
            const SizedBox(height: 6),
            Text(
              "Materials: ${activity['materials_needed']}",
              style: TextStyle(
                fontSize: isLarge ? 12 : 11,
                color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSocialEmotionalEnhancements(BuildContext context, Map<String, dynamic> recommendations, bool isDark, ThemeData theme) {
    final socialEmotional = recommendations['social_emotional_enhancements'] as List?;
    if (socialEmotional == null || socialEmotional.isEmpty) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    return Card(
      elevation: 1,
      color: theme.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: isDark ? const Color(0xFF831843) : const Color(0xFFFCE7F3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.favorite,
                    color: isDark ? Colors.pink[200] : const Color(0xFFDB2777),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Emotional Development",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...socialEmotional.map((item) => _buildSocialEmotionalCard(item, isDark, theme, isLarge)),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialEmotionalCard(dynamic item, bool isDark, ThemeData theme, bool isLarge) {
    if (item is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(isLarge ? 16 : 14),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF23272F) : const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: isDark ? const Color(0xFF374151) : Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item['area']?.toString() ?? 'Emotional Area',
            style: TextStyle(
              fontSize: isLarge ? 16 : 15,
              fontWeight: FontWeight.w600,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          if (item['development_activities'] != null) ...[
            const SizedBox(height: 12),
            ...((item['development_activities'] as List?) ?? []).map((activity) => 
              _buildActivityCard(activity, isDark, theme, isLarge)
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCommunicationEnhancements(BuildContext context, Map<String, dynamic> recommendations, bool isDark, ThemeData theme) {
    final communication = recommendations['communication_skill_enhancements'] as List?;
    if (communication == null || communication.isEmpty) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    return Card(
      elevation: 1,
      color: theme.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: isDark ? const Color(0xFF1E3A8A) : const Color(0xFFDBEAFE),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.chat_bubble_outline,
                    color: isDark ? Colors.blue[200] : const Color(0xFF2563EB),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Communication Skills",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...communication.map((item) => _buildCommunicationCard(item, isDark, theme, isLarge)),
          ],
        ),
      ),
    );
  }

  Widget _buildCommunicationCard(dynamic item, bool isDark, ThemeData theme, bool isLarge) {
    if (item is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(isLarge ? 16 : 14),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF23272F) : const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: isDark ? const Color(0xFF374151) : Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item['skill_area']?.toString() ?? 'Communication Skill',
            style: TextStyle(
              fontSize: isLarge ? 16 : 15,
              fontWeight: FontWeight.w600,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          if (item['enhancement_activities'] != null) ...[
            const SizedBox(height: 12),
            ...((item['enhancement_activities'] as List?) ?? []).map((activity) => 
              _buildActivityCard(activity, isDark, theme, isLarge)
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildParentCollaborationTips(BuildContext context, Map<String, dynamic> recommendations, bool isDark, ThemeData theme) {
    final collaboration = recommendations['collaborative_next_steps_for_parent_educator'] as Map<String, dynamic>?;
    if (collaboration == null) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    return Card(
      elevation: 1,
      color: theme.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: isDark ? const Color(0xFF365314) : const Color(0xFFDCFCE7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.family_restroom,
                    color: isDark ? Colors.green[200] : const Color(0xFF16A34A),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Tips for Home",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (collaboration['home_based_reinforcement_ideas'] != null) ...[
              Text(
                "Activities to Try at Home:",
                style: TextStyle(
                  fontSize: isLarge ? 16 : 15,
                  fontWeight: FontWeight.w600,
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 8),
              ...((collaboration['home_based_reinforcement_ideas'] as List?) ?? []).map((idea) => 
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.home, size: 16, color: isDark ? Colors.green[200] : Colors.green[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          idea.toString(),
                          style: TextStyle(
                            fontSize: isLarge ? 14 : 13,
                            color: theme.textTheme.bodyMedium?.color,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            if (collaboration['monitoring_progress_tips'] != null) ...[
              const SizedBox(height: 16),
              Text(
                "Tracking Progress:",
                style: TextStyle(
                  fontSize: isLarge ? 16 : 15,
                  fontWeight: FontWeight.w600,
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(isLarge ? 14 : 12),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF23272F) : const Color(0xFFF9FAFB),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: isDark ? const Color(0xFF374151) : Colors.grey.shade200),
                ),
                child: Text(
                  collaboration['monitoring_progress_tips'].toString(),
                  style: TextStyle(
                    fontSize: isLarge ? 14 : 13,
                    color: theme.textTheme.bodyMedium?.color,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, bool isDark, ThemeData theme) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: _getCardMaxWidth(context) ?? double.infinity),
        margin: _getResponsivePadding(context),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF2D3748) : const Color(0xFFFEF2F2),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: isDark ? const Color(0xFF4B5563) : const Color(0xFFFEE2E2),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(Icons.error_outline, color: isDark ? Colors.orange[200] : const Color(0xFFEF4444), size: 40),
            ),
            const SizedBox(height: 20),
            Text(
              _errorMessage ?? 'Something went wrong',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isDark ? Colors.orange[200] : const Color(0xFFB91C1C),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Please try again later or contact support if the issue persists.',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isDark ? Colors.grey[400] : const Color(0xFF6B7280),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}