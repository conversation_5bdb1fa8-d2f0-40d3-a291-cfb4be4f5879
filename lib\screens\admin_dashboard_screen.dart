import 'package:flutter/material.dart';
import '../services/admin_dashboard_service.dart';
import '../services/auth_service.dart';
import '../services/serial_service.dart';
import '../models/user_profile.dart';

class AdminDashboardScreen extends StatefulWidget {
  final SerialService serialService;
  final AuthService authService;

  const AdminDashboardScreen({
    super.key,
    required this.serialService,
    required this.authService,
  });

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  List<Map<String, dynamic>> _users = [];
  List<Map<String, dynamic>> _qrCodes = [];
  List<Map<String, dynamic>> _subscriptions = [];
  bool _isLoading = true;
  String _searchQuery = '';
  UserProfile? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadData();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    final user = await widget.authService.getCurrentUser();
    if (mounted) {
      setState(() {
        _currentUser = user;
      });
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Load data with individual error handling
      List<Map<String, dynamic>> users = [];
      List<Map<String, dynamic>> qrCodes = [];
      List<Map<String, dynamic>> subscriptions = [];

      try {
        users = await AdminDashboardService.getRegisteredUsers();
      } catch (e) {
        debugPrint('Error loading users: $e');
      }

      try {
        qrCodes = await AdminDashboardService.getAllQRCodes();
      } catch (e) {
        debugPrint('Error loading QR codes: $e');
      }

      try {
        subscriptions = await AdminDashboardService.getAllSubscriptions();
      } catch (e) {
        debugPrint('Error loading subscriptions: $e');
      }

      if (mounted) {
        setState(() {
          _users = users;
          _qrCodes = qrCodes;
          _subscriptions = subscriptions;
        });
      }
    } catch (e) {
      debugPrint('General error loading admin data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading some admin data. Check console for details.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _searchUsers() async {
    if (_searchQuery.isEmpty) {
      await _loadData();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final results = await AdminDashboardService.searchUsers(_searchQuery);
      setState(() {
        _users = results;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error searching users: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateQRCodes() async {
    final TextEditingController countController = TextEditingController(text: '1');
    
    final count = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Generate QR Codes'),
        content: TextField(
          controller: countController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Number of QR codes to generate',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final count = int.tryParse(countController.text);
              Navigator.pop(context, count);
            },
            child: const Text('Generate'),
          ),
        ],
      ),
    );

    if (count == null || count <= 0) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final serials = await AdminDashboardService.generateSerialNumbers(count);
      await _loadData();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Generated $count QR codes successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error generating QR codes: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _assignQRCode(Map<String, dynamic> user) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get available QR codes
      final availableQRCodes = await AdminDashboardService.getAvailableQRCodes();
      
      setState(() {
        _isLoading = false;
      });
      
      if (availableQRCodes.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No QR codes available for assignment')),
        );
        return;
      }

      final qrCode = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Assign QR Code'),
          content: DropdownButtonFormField<Map<String, dynamic>>(
            decoration: const InputDecoration(
              labelText: 'Select QR Code',
            ),
            items: availableQRCodes.map((qr) {
              return DropdownMenuItem<Map<String, dynamic>>(
                value: qr,
                child: Text(qr['serial']),
              );
            }).toList(),
            onChanged: (value) => Navigator.pop(context, value),
          ),
        ),
      );

      if (qrCode == null) return;

      setState(() {
        _isLoading = true;
      });

      await AdminDashboardService.assignQRCodeToUser(
        user['id'],
        qrCode['serial'],
      );
      
      await _loadData();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('QR code assigned successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error assigning QR code: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      initialIndex: 0, // Explicitly set initial index
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Admin Dashboard'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Users'),
              Tab(text: 'Subscriptions'),
              Tab(text: 'QR Codes'),
            ],
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.qr_code),
              onPressed: _generateQRCodes,
              tooltip: 'Generate QR Codes',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadData,
              tooltip: 'Refresh',
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Builder(
                builder: (context) {
                  try {
                    return TabBarView(
                      children: [
                        _buildUsersTab(),
                        _buildSubscriptionsTab(),
                        _buildQRCodesTab(),
                      ],
                    );
                  } catch (e) {
                    debugPrint('Error building TabBarView: $e');
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error, size: 64, color: Colors.red),
                          const SizedBox(height: 16),
                          Text('Error loading admin dashboard: $e'),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => setState(() {}),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
      ),
    );
  }

  Widget _buildUsersTab() {
    return _users.isEmpty
        ? const Center(child: Text('No users found'))
        : ListView.builder(
            itemCount: _users.length,
            itemBuilder: (context, index) {
              final user = _users[index];
              final hasQrCode = user['qr_code'] != null;
              final hasActiveSubscription = user['has_active_subscription'] ?? false;
              
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  title: Text(user['name'] ?? 'Unknown'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(user['email'] ?? 'No email'),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          _buildStatusChip(
                            hasQrCode ? 'QR Linked' : 'No QR',
                            hasQrCode ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          _buildStatusChip(
                            hasActiveSubscription ? 'Subscription Active' : 'No Subscription',
                            hasActiveSubscription ? Colors.green : Colors.orange,
                          ),
                        ],
                      ),
                    ],
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (!hasQrCode)
                        IconButton(
                          icon: const Icon(Icons.qr_code),
                          tooltip: 'Assign QR Code',
                          onPressed: () {
                            // Implement QR code assignment
                          },
                        ),
                      IconButton(
                        icon: const Icon(Icons.edit),
                        tooltip: 'Edit User',
                        onPressed: () {
                          // Navigate to user edit screen
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }

  Widget _buildSubscriptionsTab() {
    if (_subscriptions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.subscriptions_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No subscriptions found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _subscriptions.length,
      itemBuilder: (context, index) {
        final subscription = _subscriptions[index];
        final isActive = subscription['status'] == 'active';
        final endDate = _parseDateTime(subscription['end_date']);
        final isExpired = endDate.isBefore(DateTime.now());
        final daysUntilExpiry = endDate.difference(DateTime.now()).inDays;

        // Determine status color and text
        Color statusColor;
        String statusText;
        if (isActive && !isExpired) {
          if (daysUntilExpiry <= 7) {
            statusColor = Colors.orange;
            statusText = 'Expires in $daysUntilExpiry days';
          } else {
            statusColor = Colors.green;
            statusText = 'Active';
          }
        } else if (isExpired) {
          statusColor = Colors.red;
          statusText = 'Expired';
        } else {
          statusColor = Colors.grey;
          statusText = subscription['status'] ?? 'Unknown';
        }

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(subscription['user_name'] ?? 'Unknown User'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(subscription['user_email'] ?? 'No email'),
                const SizedBox(height: 4),
                Text('Serial: ${subscription['serial_number'] ?? 'N/A'}'),
                const SizedBox(height: 4),
                Text('End Date: ${_formatDate(endDate)}'),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildStatusChip(statusText, statusColor),
                    if (subscription['stripe_subscription_id'] != null) ...[
                      const SizedBox(width: 8),
                      _buildStatusChip('Stripe', Colors.blue),
                    ],
                  ],
                ),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'ID: ${subscription['subscription_id']}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const SizedBox(height: 4),
                if (isActive && !isExpired)
                  Icon(Icons.check_circle, color: statusColor, size: 20)
                else
                  Icon(Icons.warning, color: statusColor, size: 20),
              ],
            ),
            onTap: () => _showSubscriptionDetails(subscription),
          ),
        );
      },
    );
  }

  Widget _buildQRCodesTab() {
    return ListView.builder(
      itemCount: _qrCodes.length,
      itemBuilder: (context, index) {
        final qrCode = _qrCodes[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            title: Text(qrCode['serial'] ?? 'No serial'),
            subtitle: Text(qrCode['assigned_to_name'] ?? 'Not assigned'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.qr_code),
                  onPressed: () {
                    // Show QR code
                  },
                  tooltip: 'View QR Code',
                ),
                if (qrCode['assigned_to_name'] == null)
                  IconButton(
                    icon: const Icon(Icons.person_add),
                    onPressed: () {
                      // Assign to user
                    },
                    tooltip: 'Assign to User',
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        label,
        style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 12),
      ),
    );
  }

  // Helper method to safely parse DateTime
  DateTime _parseDateTime(dynamic dateValue) {
    if (dateValue is DateTime) {
      return dateValue;
    } else if (dateValue is String) {
      try {
        return DateTime.parse(dateValue);
      } catch (e) {
        debugPrint('Error parsing date: $e');
      }
    }
    return DateTime.now(); // Fallback
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Show subscription details dialog
  void _showSubscriptionDetails(Map<String, dynamic> subscription) {
    final endDate = _parseDateTime(subscription['end_date']);
    final startDate = _parseDateTime(subscription['start_date']);
    final isActive = subscription['status'] == 'active';
    final isExpired = endDate.isBefore(DateTime.now());
    final daysUntilExpiry = endDate.difference(DateTime.now()).inDays;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Subscription Details'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow('User', subscription['user_name'] ?? 'Unknown'),
                _buildDetailRow('Email', subscription['user_email'] ?? 'N/A'),
                _buildDetailRow('Serial Number', subscription['serial_number'] ?? 'N/A'),
                _buildDetailRow('Status', subscription['status'] ?? 'Unknown'),
                _buildDetailRow('Start Date', _formatDate(startDate)),
                _buildDetailRow('End Date', _formatDate(endDate)),
                if (isActive && !isExpired)
                  _buildDetailRow('Days Until Expiry', '$daysUntilExpiry days'),
                if (subscription['stripe_subscription_id'] != null)
                  _buildDetailRow('Stripe ID', subscription['stripe_subscription_id']),
                if (subscription['stripe_price_id'] != null)
                  _buildDetailRow('Price ID', subscription['stripe_price_id']),
                _buildDetailRow('Created', _formatDate(_parseDateTime(subscription['subscription_created_at']))),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
            if (isActive && !isExpired)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showCancelSubscriptionDialog(subscription);
                },
                child: const Text('Cancel Subscription', style: TextStyle(color: Colors.red)),
              ),
          ],
        );
      },
    );
  }

  // Helper to build detail rows
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  // Show cancel subscription dialog
  void _showCancelSubscriptionDialog(Map<String, dynamic> subscription) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Cancel Subscription'),
          content: Text(
            'Are you sure you want to cancel the subscription for ${subscription['user_name']}?\n\n'
            'This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _cancelSubscription(subscription);
              },
              child: const Text('Confirm Cancel', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  // Cancel subscription
  Future<void> _cancelSubscription(Map<String, dynamic> subscription) async {
    try {
      // Update subscription status to cancelled
      await AdminDashboardService.cancelSubscription(subscription['subscription_id']);

      // Reload data
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Subscription cancelled for ${subscription['user_name']}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cancelling subscription: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
