import 'package:flutter/material.dart';
import '../services/theme_provider.dart';
import '../services/payment_processor.dart';
import 'package:provider/provider.dart';

class NoSubscriptionScreen extends StatefulWidget {
  final VoidCallback onLogout;
  final int userId;
  final VoidCallback? onSubscriptionFound;

  const NoSubscriptionScreen({
    super.key,
    required this.onLogout,
    required this.userId,
    this.onSubscriptionFound,
  });

  @override
  State<NoSubscriptionScreen> createState() => _NoSubscriptionScreenState();
}

class _NoSubscriptionScreenState extends State<NoSubscriptionScreen> {
  bool _isRefreshing = false;

  void _navigateToSubscription(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/subscription',
      arguments: {'userId': widget.userId},
    );
  }

  Future<void> _refreshSubscriptionStatus() async {
    if (_isRefreshing) return;

    // Check if user ID is valid
    if (widget.userId <= 0) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid user session. Please log in again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('Refreshing subscription status for user ${widget.userId}...');
      await PaymentProcessor.syncWithStripe(widget.userId);

      // Check if subscription is now active
      final hasActiveSubscription = await PaymentProcessor.verifyActiveSubscription(widget.userId);
      debugPrint('After refresh, subscription status: $hasActiveSubscription');

      if (hasActiveSubscription && widget.onSubscriptionFound != null) {
        // Subscription found, trigger callback to refresh parent page
        widget.onSubscriptionFound!();
      } else if (hasActiveSubscription) {
        // If no callback provided, just pop this screen
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        // Still no subscription, show message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No active subscription found. Please subscribe to continue.'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      print('Error refreshing subscription: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking subscription: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final theme = Theme.of(context);
    final isDark = themeProvider.isDarkMode;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Subscription Required'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        foregroundColor: theme.appBarTheme.foregroundColor,
        actions: [
          IconButton(
            icon: _isRefreshing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh),
            tooltip: 'Check Subscription Status',
            onPressed: _isRefreshing ? null : _refreshSubscriptionStatus,
          ),
          IconButton(
            icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
            tooltip: isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode',
            onPressed: () => themeProvider.toggleTheme(),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          gradient: isDark
              ? null
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [const Color(0xFFF3E8FF), Colors.white],
                ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 400),
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: theme.cardColor,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFEF3C7), // amber-100
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: const Icon(
                        Icons.subscriptions_outlined,
                        color: Color(0xFFD97706), // amber-600
                        size: 48,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      "Subscription Required",
                      style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      "You need an active subscription to access the Learning Coach feature. Subscribe to unlock personalized insights and recommendations for your child's learning journey.",
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodyMedium?.copyWith(height: 1.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      "If you've already subscribed, tap the refresh button to check your subscription status.",
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 24),
                    TextButton.icon(
                      onPressed: _isRefreshing ? null : _refreshSubscriptionStatus,
                      icon: _isRefreshing
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8B5CF6)),
                              ),
                            )
                          : const Icon(Icons.refresh, size: 18),
                      label: Text(_isRefreshing ? 'Checking...' : 'Check Subscription Status'),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF8B5CF6),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _navigateToSubscription(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF8B5CF6), // purple-600
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: const Text(
                          'View Subscription Options',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: widget.onLogout,
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.textTheme.bodyMedium?.color,
                          side: BorderSide(color: theme.dividerColor),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Sign Out',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
