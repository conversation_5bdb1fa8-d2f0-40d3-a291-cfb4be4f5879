import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:immy_app/services/api_key_service.dart';

// Generate mocks
@GenerateMocks([SharedPreferences])
void main() {
  group('ApiKeyService Tests', () {
    setUpAll(() {
      // Initialize test environment
    });

    group('API Key Validation', () {
      test('should validate non-empty API key', () {
        const validKey = 'any-api-key-here';
        expect(ApiKeyService.isValidApiKey(validKey), isTrue);
      });

      test('should reject empty API key', () {
        expect(ApiKeyService.isValidApiKey(''), isFalse);
      });

      test('should handle whitespace in API key', () {
        const keyWithSpaces = '  any-api-key  ';
        expect(ApiKeyService.isValidApiKey(keyWithSpaces), isTrue);
      });

      test('should reject only whitespace', () {
        expect(ApiKeyService.isValidApiKey('   '), isFalse);
      });
    });



    group('API Key Storage', () {
      test('should save valid API key', () async {
        // Mock SharedPreferences
        SharedPreferences.setMockInitialValues({});
        
        const validKey = 'sk-1234567890abcdef1234567890abcdef1234567890abcdef';
        final result = await ApiKeyService.saveApiKey(validKey);
        
        expect(result, isTrue);
      });

      test('should reject invalid API key for saving', () async {
        SharedPreferences.setMockInitialValues({});
        
        const invalidKey = 'invalid';
        final result = await ApiKeyService.saveApiKey(invalidKey);
        
        expect(result, isFalse);
      });

      test('should retrieve saved API key', () async {
        const testKey = 'sk-1234567890abcdef1234567890abcdef1234567890abcdef';
        SharedPreferences.setMockInitialValues({'user_api_key': testKey});
        
        final retrievedKey = await ApiKeyService.getApiKey();
        expect(retrievedKey, equals(testKey));
      });

      test('should return null when no API key is saved', () async {
        SharedPreferences.setMockInitialValues({});
        
        final retrievedKey = await ApiKeyService.getApiKey();
        expect(retrievedKey, isNull);
      });

      test('should clear API key', () async {
        const testKey = 'sk-1234567890abcdef1234567890abcdef1234567890abcdef';
        SharedPreferences.setMockInitialValues({
          'user_api_key': testKey,
          'api_key_validated': true,
        });
        
        await ApiKeyService.clearApiKey();
        
        final retrievedKey = await ApiKeyService.getApiKey();
        final isValidated = await ApiKeyService.isApiKeyValidated();
        
        expect(retrievedKey, isNull);
        expect(isValidated, isFalse);
      });
    });

    group('API Key Validation Status', () {
      test('should return true when API key is validated', () async {
        SharedPreferences.setMockInitialValues({'api_key_validated': true});
        
        final isValidated = await ApiKeyService.isApiKeyValidated();
        expect(isValidated, isTrue);
      });

      test('should return false when API key is not validated', () async {
        SharedPreferences.setMockInitialValues({});
        
        final isValidated = await ApiKeyService.isApiKeyValidated();
        expect(isValidated, isFalse);
      });
    });

    group('Masked API Key Display', () {
      test('should mask API key for display', () async {
        const testKey = 'sk-1234567890abcdef1234567890abcdef1234567890abcdef';
        SharedPreferences.setMockInitialValues({'user_api_key': testKey});
        
        final maskedKey = await ApiKeyService.getMaskedApiKey();
        expect(maskedKey, isNotNull);
        expect(maskedKey!.startsWith('sk-1'), isTrue);
        expect(maskedKey.endsWith('cdef'), isTrue);
        expect(maskedKey.contains('*'), isTrue);
        expect(maskedKey.length, equals(testKey.length));
      });

      test('should return null when no API key exists', () async {
        SharedPreferences.setMockInitialValues({});
        
        final maskedKey = await ApiKeyService.getMaskedApiKey();
        expect(maskedKey, isNull);
      });
    });

    group('Pi Communication Data Preparation', () {
      test('should prepare credentials data correctly', () async {
        const apiKey = 'any-api-key';
        const username = 'TestUser';
        const email = '<EMAIL>';
        const userId = 123;

        // This test would require mocking HTTP calls
        // For now, we'll test the data validation
        expect(ApiKeyService.isValidApiKey(apiKey), isTrue);
        expect(username.isNotEmpty, isTrue);
        expect(email.contains('@'), isTrue);
        expect(userId > 0, isTrue);
      });
    });

    group('External API Validation', () {
      test('should validate non-empty API key externally', () async {
        const validKey = 'any-api-key';
        final result = await ApiKeyService.validateApiKeyWithService(validKey);

        expect(result['valid'], isTrue);
        expect(result['message'], isNotNull);
      });

      test('should reject empty API key externally', () async {
        const invalidKey = '';
        final result = await ApiKeyService.validateApiKeyWithService(invalidKey);

        expect(result['valid'], isFalse);
        expect(result['error'], isNotNull);
      });
    });

    group('Error Handling', () {
      test('should handle storage errors gracefully', () async {
        // This would test error scenarios in a real implementation
        expect(() async => await ApiKeyService.getApiKey(), returnsNormally);
      });

      test('should handle network errors gracefully', () async {
        // This would test network error scenarios
        expect(() async => await ApiKeyService.testPiConnection(), returnsNormally);
      });
    });

    group('Integration Tests', () {
      test('should complete full API key workflow', () async {
        SharedPreferences.setMockInitialValues({});
        
        const testKey = 'sk-1234567890abcdef1234567890abcdef1234567890abcdef';
        
        // 1. Validate format
        expect(ApiKeyService.isValidApiKey(testKey), isTrue);
        
        // 2. Save API key
        final saveResult = await ApiKeyService.saveApiKey(testKey);
        expect(saveResult, isTrue);
        
        // 3. Retrieve API key
        final retrievedKey = await ApiKeyService.getApiKey();
        expect(retrievedKey, equals(testKey));
        
        // 4. Check validation status
        final isValidated = await ApiKeyService.isApiKeyValidated();
        expect(isValidated, isTrue);
        
        // 5. Get masked version
        final maskedKey = await ApiKeyService.getMaskedApiKey();
        expect(maskedKey, isNotNull);
        expect(maskedKey!.contains('*'), isTrue);
        
        // 6. Clear API key
        await ApiKeyService.clearApiKey();
        final clearedKey = await ApiKeyService.getApiKey();
        expect(clearedKey, isNull);
      });
    });
  });
}
