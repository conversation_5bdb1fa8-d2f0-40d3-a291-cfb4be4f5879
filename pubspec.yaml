name: immy_app
description: "A Flutter application for <PERSON><PERSON>, the AI-powered teddy bear."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  # flutter_local_notifications: ^19.2.0  # Temporarily disabled for Windows build
  # timezone: ^0.10.1  # Temporarily disabled for Windows build
  cupertino_icons: ^1.0.2
  shared_preferences: ^2.5.3
  flutter_svg: ^2.1.0
  flutter_icons_null_safety: ^1.1.0
  qr_flutter: ^4.1.0
  http: ^1.3.0  # ✅ Already included - perfect for email service
  http_parser: ^4.0.2
  uuid: ^4.5.1
  path_provider: ^2.1.0
  crypto: ^3.0.3
  # mobile_scanner: 3.4.1  # Temporarily disabled due to build issues
  mysql1: ^0.20.0
  provider: ^6.0.5  # ✅ Already included - needed for theme provider
  shelf: ^1.4.1
  shelf_router: ^1.1.4
  url_launcher: ^6.3.1
  flutter_stripe: ^11.1.0
  intl: ^0.20.2
  flutter_launcher_icons: ^0.14.3
  mailer: ^6.4.1
  webview_flutter: ^4.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  build_runner: ^2.3.3

flutter:
  uses-material-design: true
  assets:
    - assets/immy_BrainyBear.png
    - assets/  # 🆕 Include entire assets folder for any additional images
